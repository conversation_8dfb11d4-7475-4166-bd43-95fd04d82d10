import { ReactNode, useEffect } from "react";
import { X } from "lucide-react";

interface BaseModalProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  subtitle?: string;
  icon?: ReactNode;
  children: ReactNode;
  size?: "sm" | "md" | "lg" | "xl" | "2xl" | "6xl";
  zIndex?: number;
  showCloseButton?: boolean;
  closeOnBackdropClick?: boolean;
}

const BaseModal = ({
  isOpen,
  onClose,
  title,
  subtitle,
  icon,
  children,
  size = "lg",
  zIndex = 50,
  showCloseButton = true,
  closeOnBackdropClick = true,
}: BaseModalProps) => {
  // Prevent body scroll when modal is open
  useEffect(() => {
    if (isOpen) {
      // Only set overflow hidden if no other modals are open
      const existingModals = document.querySelectorAll('[data-modal="true"]');
      if (existingModals.length === 0) {
        document.body.style.overflow = "hidden";
      }
    } else {
      // Only restore overflow if this is the last modal closing
      const existingModals = document.querySelectorAll('[data-modal="true"]');
      if (existingModals.length <= 1) {
        document.body.style.overflow = "unset";
      }
    }

    // Cleanup on unmount
    return () => {
      const existingModals = document.querySelectorAll('[data-modal="true"]');
      if (existingModals.length <= 1) {
        document.body.style.overflow = "unset";
      }
    };
  }, [isOpen]);

  // Handle escape key
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === "Escape" && isOpen) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener("keydown", handleEscape);
    }

    return () => {
      document.removeEventListener("keydown", handleEscape);
    };
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  const getSizeClasses = () => {
    switch (size) {
      case "sm":
        return "max-w-sm mx-2 sm:mx-auto sm:max-w-sm";
      case "md":
        return "max-w-md mx-2 sm:mx-auto sm:max-w-md";
      case "lg":
        return "max-w-lg mx-2 sm:mx-auto sm:max-w-lg";
      case "xl":
        return "max-w-xl mx-2 sm:mx-auto sm:max-w-xl";
      case "2xl":
        return "max-w-2xl mx-2 sm:mx-auto sm:max-w-2xl";
      case "6xl":
        return "max-w-full mx-2 sm:mx-auto sm:max-w-6xl";
      default:
        return "max-w-lg mx-2 sm:mx-auto sm:max-w-lg";
    }
  };

  const handleBackdropClick = () => {
    // Disable backdrop closing for mobile-friendly experience
    // Only close via explicit close button/icon clicks
    return;
  };

  return (
    <div
      className={`fixed inset-0 overflow-y-auto`}
      style={{ zIndex: zIndex }}
      data-modal="true"
    >
      <div className="flex items-end sm:items-center justify-center min-h-screen pt-0 px-0 pb-0 text-center sm:pt-4 sm:px-4 sm:pb-20 sm:block sm:p-0">
        {/* Backdrop */}
        {/* <div
          className="fixed inset-0 bg-opacity-50 transition-opacity"
          onClick={handleBackdropClick}
          aria-hidden="true"
        /> */}

        {/* Center alignment helper */}
        <span
          className="hidden sm:inline-block sm:align-middle sm:h-screen"
          aria-hidden="true"
        >
          &#8203;
        </span>

        {/* Modal panel */}
        <div
          className={`inline-block align-bottom bg-white rounded-t-xl sm:rounded-lg text-left shadow-xl transform transition-all w-full sm:my-8 sm:align-middle ${getSizeClasses()}`}
          style={{ maxHeight: "90vh" }}
        >
          <div className="bg-white flex flex-col max-h-full">
            {/* Header */}
            {(title || showCloseButton) && (
              <div className="flex-shrink-0 px-3 pt-4 sm:px-4 sm:pt-5 lg:px-6 lg:pt-6">
                <div className="flex items-center justify-between mb-4 sm:mb-6">
                  {(title || icon) && (
                    <div className="flex items-center min-w-0 flex-1">
                      {icon && (
                        <div className="flex-shrink-0 flex items-center justify-center h-8 w-8 sm:h-10 sm:w-10 lg:h-12 lg:w-12 rounded-full bg-blue-100">
                          {icon}
                        </div>
                      )}
                      {title && (
                        <div
                          className={`${icon ? "ml-3 sm:ml-4" : ""} min-w-0`}
                        >
                          <h3 className="text-base sm:text-lg leading-6 font-medium text-gray-900 truncate">
                            {title}
                          </h3>
                          {subtitle && (
                            <p className="text-xs sm:text-sm text-gray-500 truncate">
                              {subtitle}
                            </p>
                          )}
                        </div>
                      )}
                    </div>
                  )}
                  {showCloseButton && (
                    <button
                      onClick={onClose}
                      className="flex-shrink-0 ml-3 rounded-md text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 p-2 touch-manipulation"
                      aria-label="Close modal"
                    >
                      <X className="h-5 w-5 sm:h-6 sm:w-6" />
                    </button>
                  )}
                </div>
              </div>
            )}

            {/* Content */}
            <div className="flex-1 overflow-y-auto px-3 pb-3 sm:px-4 sm:pb-4 lg:px-6 lg:pb-6">
              {children}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BaseModal;
