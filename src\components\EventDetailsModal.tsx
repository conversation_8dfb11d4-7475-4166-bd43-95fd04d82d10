import { useState } from "react";
import {
  CalendarIcon,
  MapPinIcon,
  UsersIcon,
  PlusIcon,
  TrashIcon,
  CheckCircleIcon,
  XCircleIcon,
  QrCodeIcon,
  MailIcon,
  Eye,
} from "lucide-react";
import BaseModal from "./BaseModal";
import { useEventStore } from "../store/eventStore";
import type { Event, AttendeeFormData } from "../types";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import QRCodeModal from "./QRCodeModal";
import { QRCodeService } from "../services/qrCodeService";
import { getModalZIndex } from "../utils/zIndex";
import { transformEvent, getEventStatusText } from "../utils/eventTransform";

const attendeeSchema = z.object({
  name: z
    .string()
    .min(1, "Name is required")
    .max(100, "Name must be less than 100 characters"),
  email: z.string().email("Invalid email address"),
});

interface EventDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  event: Event | null;
}

const EventDetailsModal = ({
  isOpen,
  onClose,
  event,
}: EventDetailsModalProps) => {
  const {
    getEventAttendees,
    addAttendee,
    removeAttendee,
    checkInAttendee,
    isLoading,
  } = useEventStore();
  const [showAddAttendee, setShowAddAttendee] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showQRModal, setShowQRModal] = useState(false);
  const [selectedAttendee, setSelectedAttendee] = useState<any>(null);
  const [isSendingEmails, setIsSendingEmails] = useState(false);

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<AttendeeFormData>({
    resolver: zodResolver(attendeeSchema),
  });

  if (!isOpen || !event) return null;

  const transformedEvent = transformEvent(event);
  const attendees = getEventAttendees(event.id);
  const checkedInCount = attendees.filter((a) => a.checkedIn).length;

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat("en-US", {
      weekday: "long",
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    }).format(new Date(date));
  };

  const getStatusColor = (status?: number) => {
    switch (status) {
      case 1:
        return "bg-green-100 text-green-800";
      case 0:
        return "bg-gray-100 text-gray-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const onSubmitAttendee = async (data: AttendeeFormData) => {
    setIsSubmitting(true);
    try {
      await addAttendee(event.id, data);
      reset();
      setShowAddAttendee(false);
    } catch (error) {
      console.error("Error adding attendee:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleRemoveAttendee = async (attendeeId: string) => {
    if (window.confirm("Are you sure you want to remove this attendee?")) {
      await removeAttendee(attendeeId);
    }
  };

  const handleCheckIn = async (attendeeId: string) => {
    await checkInAttendee(attendeeId);
  };

  const handleShowQRCode = (attendee: any) => {
    setSelectedAttendee(attendee);
    setShowQRModal(true);
  };

  const handleSendAllQRCodes = async () => {
    if (!event) return;

    setIsSendingEmails(true);
    try {
      const attendees = getEventAttendees(event.id);

      // Simulate sending emails to all attendees
      for (const attendee of attendees) {
        const emailData = await QRCodeService.generateEmailQRCode(
          attendee.id,
          event.id,
          attendee.name,
          event.name
        );

        // In a real app, this would send actual emails
        console.log(`QR code email generated for ${attendee.email}`, emailData);

        // Add small delay to simulate email sending
        await new Promise((resolve) => setTimeout(resolve, 200));
      }

      alert(`QR code emails would be sent to ${attendees.length} attendees`);
    } catch (error) {
      console.error("Error sending QR code emails:", error);
      alert("Failed to send some QR code emails. Please try again.");
    } finally {
      setIsSendingEmails(false);
    }
  };

  return (
    <BaseModal
      isOpen={isOpen}
      onClose={onClose}
      title="Event Details"
      subtitle="Manage attendees and event information"
      icon={<Eye className="h-6 w-6 text-blue-600" />}
      size="6xl"
      zIndex={getModalZIndex("EVENT_DETAILS_MODAL")}
    >
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Event Information */}
        <div className="space-y-4">
          <div>
            <h4 className="text-xl font-semibold text-gray-900">
              {transformedEvent.name}
            </h4>
            <span
              className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium mt-2 ${getStatusColor(
                transformedEvent.status
              )}`}
            >
              {getEventStatusText(transformedEvent.status || 0)}
            </span>
          </div>

          <div>
            <h5 className="text-sm font-medium text-gray-700 mb-1">
              Description
            </h5>
            <p className="text-sm text-gray-600">
              {transformedEvent.description}
            </p>
          </div>

          <div className="space-y-2">
            <div className="flex items-center text-sm text-gray-600">
              <CalendarIcon className="h-4 w-4 mr-2" />
              <div>
                <div>Start: {formatDate(transformedEvent.startDate!)}</div>
                <div>End: {formatDate(transformedEvent.endDate!)}</div>
              </div>
            </div>
            <div className="flex items-center text-sm text-gray-600">
              <MapPinIcon className="h-4 w-4 mr-2" />
              {transformedEvent.location}
            </div>
            <div className="flex items-center text-sm text-gray-600">
              <UsersIcon className="h-4 w-4 mr-2" />
              {attendees.length} registered
              {transformedEvent.maxAttendees &&
                ` / ${transformedEvent.maxAttendees} max`}
            </div>
          </div>

          <div className="bg-gray-50 p-4 rounded-lg">
            <h5 className="text-sm font-medium text-gray-700 mb-2">
              Check-in Statistics
            </h5>
            <div className="text-2xl font-bold text-blue-600">
              {checkedInCount} / {attendees.length}
            </div>
            <div className="text-sm text-gray-500">
              {attendees.length > 0
                ? Math.round((checkedInCount / attendees.length) * 100)
                : 0}
              % checked in
            </div>
          </div>
        </div>

        {/* Attendees List */}
        <div>
          <div className="flex items-center justify-between mb-4">
            <h5 className="text-lg font-medium text-gray-900">Attendees</h5>
            <div className="flex space-x-2">
              <button
                onClick={handleSendAllQRCodes}
                disabled={isSendingEmails || attendees.length === 0}
                className="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <MailIcon className="h-4 w-4 mr-1" />
                {isSendingEmails ? "Sending..." : "Send All QR Codes"}
              </button>
              <button
                onClick={() => setShowAddAttendee(true)}
                className="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-blue-600 bg-blue-100 hover:bg-blue-200"
              >
                <PlusIcon className="h-4 w-4 mr-1" />
                Add Attendee
              </button>
            </div>
          </div>

          {showAddAttendee && (
            <div className="mb-4 p-4 border border-gray-200 rounded-lg bg-gray-50">
              <form
                onSubmit={handleSubmit(onSubmitAttendee)}
                className="space-y-3"
              >
                <div>
                  <input
                    {...register("name")}
                    type="text"
                    placeholder="Attendee name"
                    className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  />
                  {errors.name && (
                    <p className="mt-1 text-sm text-red-600">
                      {errors.name.message}
                    </p>
                  )}
                </div>
                <div>
                  <input
                    {...register("email")}
                    type="email"
                    placeholder="Attendee email"
                    className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  />
                  {errors.email && (
                    <p className="mt-1 text-sm text-red-600">
                      {errors.email.message}
                    </p>
                  )}
                </div>
                <div className="flex justify-end space-x-2">
                  <button
                    type="button"
                    onClick={() => {
                      setShowAddAttendee(false);
                      reset();
                    }}
                    className="px-3 py-1 text-sm text-gray-600 hover:text-gray-800"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    disabled={isSubmitting}
                    className="px-3 py-1 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700 disabled:opacity-50"
                  >
                    {isSubmitting ? "Adding..." : "Add"}
                  </button>
                </div>
              </form>
            </div>
          )}

          <div className="max-h-96 overflow-y-auto">
            {attendees.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <UsersIcon className="h-12 w-12 mx-auto mb-2 text-gray-300" />
                <p>No attendees registered yet</p>
              </div>
            ) : (
              <div className="space-y-2">
                {attendees.map((attendee) => (
                  <div
                    key={attendee.id}
                    className="flex items-center justify-between p-3 border border-gray-200 rounded-lg"
                  >
                    <div className="flex-1">
                      <div className="flex items-center">
                        <div className="flex-1">
                          <p className="text-sm font-medium text-gray-900">
                            {attendee.name}
                          </p>
                          <p className="text-sm text-gray-500">
                            {attendee.email}
                          </p>
                        </div>
                        <div className="flex items-center space-x-2">
                          {attendee.checkedIn ? (
                            <div className="flex items-center text-green-600">
                              <CheckCircleIcon className="h-4 w-4 mr-1" />
                              <span className="text-xs">Checked in</span>
                            </div>
                          ) : (
                            <button
                              onClick={() => handleCheckIn(attendee.id)}
                              disabled={isLoading}
                              className="flex items-center text-blue-600 hover:text-blue-800 text-xs"
                            >
                              <XCircleIcon className="h-4 w-4 mr-1" />
                              Check in
                            </button>
                          )}
                          <button
                            onClick={() => handleShowQRCode(attendee)}
                            className="text-purple-600 hover:text-purple-800"
                            title="View QR Code"
                          >
                            <QrCodeIcon className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() => handleRemoveAttendee(attendee.id)}
                            className="text-red-600 hover:text-red-800"
                          >
                            <TrashIcon className="h-4 w-4" />
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>

      <div className="flex justify-end mt-6">
        <button
          onClick={onClose}
          className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          Close
        </button>
      </div>

      {/* QR Code Modal */}
      <QRCodeModal
        isOpen={showQRModal}
        onClose={() => {
          setShowQRModal(false);
          setSelectedAttendee(null);
        }}
        attendee={selectedAttendee}
        event={event}
      />
    </BaseModal>
  );
};

export default EventDetailsModal;
