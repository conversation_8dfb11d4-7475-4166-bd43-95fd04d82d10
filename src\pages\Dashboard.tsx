import { useEffect } from "react";
import { Calendar, Users, QrCode, TrendingUp, BarChart3 } from "lucide-react";
import { useEventStore } from "../store/eventStore";
import AnalyticsWidget from "../components/AnalyticsWidget";
import { Link } from "react-router-dom";
import { EventDateStatus } from "../types";
import { formatDate } from "../utils/helpers";

const Dashboard = () => {
  const { events, attendees, initializeMockData, getEventAttendees } =
    useEventStore();

  useEffect(() => {
    // Initialize with mock data if no events exist
    if (events.length === 0) {
      initializeMockData();
    }
  }, [events.length, initializeMockData]);

  // Calculate statistics from real data
  const totalAttendees = attendees.length;
  const totalCheckedIn = attendees.filter((a) => a.checkedIn).length;
  const checkInRate =
    totalAttendees > 0 ? (totalCheckedIn / totalAttendees) * 100 : 0;

  const stats = [
    {
      name: "Total Events",
      value: events.length.toString(),
      icon: Calendar,
      change: "+2.1%",
      changeType: "positive" as const,
    },
    {
      name: "Total Attendees",
      value: totalAttendees.toLocaleString(),
      icon: Users,
      change: "+15.3%",
      changeType: "positive" as const,
    },
    {
      name: "QR Codes Scanned",
      value: totalCheckedIn.toLocaleString(),
      icon: QrCode,
      change: "+8.2%",
      changeType: "positive" as const,
    },
    {
      name: "Check-in Rate",
      value: `${checkInRate.toFixed(1)}%`,
      icon: TrendingUp,
      change: "+3.1%",
      changeType: "positive" as const,
    },
  ];

  // Get recent events with real attendee data
  const recentEvents = events.slice(0, 3).map((eventStageDate) => {
    const event = eventStageDate.event_stage.event;
    const eventAttendees = getEventAttendees(event.id);
    const checkedInCount = eventAttendees.filter((a) => a.checkedIn).length;
    return {
      id: event.id,
      name: event.name_vi,
      date: formatDate(eventStageDate.show_date),
      attendees: eventAttendees.length,
      checkedIn: checkedInCount,
      status:
        eventStageDate.status === EventDateStatus.ONGOING
          ? "active"
          : eventStageDate.status === EventDateStatus.COMPLETED
            ? "completed"
            : "upcoming",
    };
  });

  return (
    <div className="space-y-4 sm:space-y-6">
      <div className="text-center sm:text-left">
        <h1 className="text-xl sm:text-2xl font-bold text-gray-900">
          Dashboard
        </h1>
        <p className="mt-1 text-sm text-gray-500">
          Welcome back! Here's what's happening with your events.
        </p>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4 sm:gap-5">
        {stats.map((stat) => {
          const Icon = stat.icon;
          return (
            <div
              key={stat.name}
              className="relative bg-white pt-4 px-3 pb-10 sm:pt-5 sm:px-4 sm:pb-12 lg:pt-6 lg:px-6 shadow rounded-lg overflow-hidden"
            >
              <dt>
                <div className="absolute bg-blue-500 rounded-md p-2 sm:p-3">
                  <Icon className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
                </div>
                <p className="ml-12 sm:ml-16 text-xs sm:text-sm font-medium text-gray-500 truncate">
                  {stat.name}
                </p>
              </dt>
              <dd className="ml-12 sm:ml-16 pb-4 sm:pb-6 flex items-baseline sm:pb-7">
                <p className="text-xl sm:text-2xl font-semibold text-gray-900">
                  {stat.value}
                </p>
                <p
                  className={`ml-1 sm:ml-2 flex items-baseline text-xs sm:text-sm font-semibold ${stat.changeType === "positive"
                      ? "text-green-600"
                      : "text-red-600"
                    }`}
                >
                  {stat.change}
                </p>
              </dd>
            </div>
          );
        })}
      </div>

      {/* Recent Events */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-3 py-4 sm:px-4 sm:py-5 lg:p-6">
          <h3 className="text-base sm:text-lg leading-6 font-medium text-gray-900">
            Recent Events
          </h3>
          <div className="mt-4 sm:mt-6 flow-root">
            <ul className="-my-3 sm:-my-5 divide-y divide-gray-200">
              {recentEvents.map((event) => (
                <li key={event.id} className="py-3 sm:py-4">
                  <div className="flex flex-col sm:flex-row sm:items-center space-y-2 sm:space-y-0 sm:space-x-4">
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900 truncate">
                        {event.name}
                      </p>
                      <p className="text-xs sm:text-sm text-gray-500">
                        {event.date} •{" "}
                        {event.attendees} attendees
                      </p>
                    </div>
                    <div className="flex flex-col sm:flex-row sm:items-center space-y-1 sm:space-y-0 sm:space-x-2">
                      <span
                        className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium self-start sm:self-auto ${event.status === "active"
                            ? "bg-green-100 text-green-800"
                            : event.status === "completed"
                              ? "bg-gray-100 text-gray-800"
                              : "bg-blue-100 text-blue-800"
                          }`}
                      >
                        {event.status}
                      </span>
                      <div className="text-xs sm:text-sm text-gray-500">
                        {event.checkedIn}/{event.attendees} checked in
                      </div>
                    </div>
                  </div>
                </li>
              ))}
            </ul>
          </div>
        </div>
      </div>

      {/* Analytics Section */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-3 py-4 sm:px-4 sm:py-5 lg:p-6">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4 space-y-2 sm:space-y-0">
            <h3 className="text-base sm:text-lg leading-6 font-medium text-gray-900 flex items-center">
              <BarChart3 className="h-4 w-4 sm:h-5 sm:w-5 mr-2" />
              Quick Analytics
            </h3>
            <Link
              to="/analytics"
              className="text-xs sm:text-sm text-blue-600 hover:text-blue-800 font-medium self-start sm:self-auto"
            >
              View Full Analytics →
            </Link>
          </div>
          <AnalyticsWidget showCharts={false} />
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
