import QRCode from "qrcode";
import type { QRCodeData } from "../types";

// Simple encryption/decryption for QR code data
const ENCRYPTION_KEY = "vtix-scanner-2024";

function simpleEncrypt(text: string): string {
  let result = "";
  for (let i = 0; i < text.length; i++) {
    const charCode =
      text.charCodeAt(i) ^ ENCRYPTION_KEY.charCodeAt(i % ENCRYPTION_KEY.length);
    result += String.fromCharCode(charCode);
  }
  return btoa(result); // Base64 encode
}

function simpleDecrypt(encryptedText: string): string {
  try {
    const decoded = atob(encryptedText); // Base64 decode
    let result = "";
    for (let i = 0; i < decoded.length; i++) {
      const charCode =
        decoded.charCodeAt(i) ^
        ENCRYPTION_KEY.charCodeAt(i % ENCRYPTION_KEY.length);
      result += String.fromCharCode(charCode);
    }
    return result;
  } catch (error) {
    throw new Error("Invalid QR code data");
  }
}

export class QRCodeService {
  /**
   * Generate QR code data with encryption
   */
  static generateQRData(attendeeId: string, eventId: string): QRCodeData {
    const timestamp = Date.now();
    const signature = this.generateSignature(attendeeId, eventId, timestamp);

    return {
      attendeeId,
      eventId,
      timestamp,
      signature,
    };
  }

  /**
   * Generate a signature for QR code validation
   */
  private static generateSignature(
    attendeeId: string,
    eventId: string,
    timestamp: number
  ): string {
    const data = `${attendeeId}-${eventId}-${timestamp}-${ENCRYPTION_KEY}`;
    // Simple hash function (in production, use a proper cryptographic hash)
    let hash = 0;
    for (let i = 0; i < data.length; i++) {
      const char = data.charCodeAt(i);
      hash = (hash << 5) - hash + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(36);
  }

  /**
   * Encrypt QR code data
   */
  static encryptQRData(qrData: QRCodeData): string {
    const jsonString = JSON.stringify(qrData);
    return simpleEncrypt(jsonString);
  }

  /**
   * Decrypt QR code data
   */
  static decryptQRData(encryptedData: string): QRCodeData {
    const decryptedString = simpleDecrypt(encryptedData);
    const qrData = JSON.parse(decryptedString) as QRCodeData;

    // Validate signature
    const expectedSignature = this.generateSignature(
      qrData.attendeeId,
      qrData.eventId,
      qrData.timestamp
    );

    if (qrData.signature !== expectedSignature) {
      throw new Error("Invalid QR code signature");
    }

    return qrData;
  }

  /**
   * Generate QR code image as data URL
   */
  static async generateQRCodeImage(
    attendeeId: string,
    eventId: string,
    options: {
      width?: number;
      margin?: number;
      color?: {
        dark?: string;
        light?: string;
      };
    } = {}
  ): Promise<string> {
    try {
      const qrData = this.generateQRData(attendeeId, eventId);
      const encryptedData = this.encryptQRData(qrData);

      const qrOptions = {
        width: options.width || 256,
        margin: options.margin || 2,
        color: {
          dark: options.color?.dark || "#000000",
          light: options.color?.light || "#FFFFFF",
        },
        errorCorrectionLevel: "M" as const,
      };

      const dataUrl = await QRCode.toDataURL(encryptedData, qrOptions);
      return dataUrl;
    } catch (error) {
      console.error("Error generating QR code:", error);
      throw new Error("Failed to generate QR code");
    }
  }

  /**
   * Generate QR code as SVG string
   */
  static async generateQRCodeSVG(
    attendeeId: string,
    eventId: string,
    options: {
      width?: number;
      margin?: number;
      color?: {
        dark?: string;
        light?: string;
      };
    } = {}
  ): Promise<string> {
    try {
      const qrData = this.generateQRData(attendeeId, eventId);
      const encryptedData = this.encryptQRData(qrData);

      const qrOptions = {
        width: options.width || 256,
        margin: options.margin || 2,
        color: {
          dark: options.color?.dark || "#000000",
          light: options.color?.light || "#FFFFFF",
        },
        errorCorrectionLevel: "M" as const,
      };

      const svg = await QRCode.toString(encryptedData, {
        type: "svg",
        ...qrOptions,
      });
      return svg;
    } catch (error) {
      console.error("Error generating QR code SVG:", error);
      throw new Error("Failed to generate QR code SVG");
    }
  }

  /**
   * Validate QR code data
   */
  static validateQRCode(encryptedData: string): {
    isValid: boolean;
    qrData?: QRCodeData;
    error?: string;
  } {
    try {
      const qrData = this.decryptQRData(encryptedData);

      // Check if QR code is not too old (24 hours)
      const maxAge = 24 * 60 * 60 * 1000; // 24 hours in milliseconds
      const age = Date.now() - qrData.timestamp;

      if (age > maxAge) {
        return {
          isValid: false,
          error: "QR code has expired",
        };
      }

      return {
        isValid: true,
        qrData,
      };
    } catch (error) {
      return {
        isValid: false,
        error: error instanceof Error ? error.message : "Invalid QR code",
      };
    }
  }

  /**
   * Generate a unique QR code identifier
   */
  static generateQRCodeId(attendeeId: string, eventId: string): string {
    const timestamp = Date.now();
    return `QR_${eventId}_${attendeeId}_${timestamp}`;
  }

  /**
   * Download QR code as image file
   */
  static downloadQRCode(dataUrl: string, filename: string): void {
    const link = document.createElement("a");
    link.download = filename;
    link.href = dataUrl;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }

  /**
   * Generate email-friendly QR code data
   */
  static async generateEmailQRCode(
    attendeeId: string,
    eventId: string,
    attendeeName: string,
    eventName: string
  ): Promise<{
    qrCodeImage: string;
    qrCodeId: string;
    emailSubject: string;
    emailBody: string;
  }> {
    const qrCodeImage = await this.generateQRCodeImage(attendeeId, eventId, {
      width: 300,
      margin: 3,
    });

    const qrCodeId = this.generateQRCodeId(attendeeId, eventId);

    const emailSubject = `Your QR Code for ${eventName}`;
    const emailBody = `
Dear ${attendeeName},

Thank you for registering for ${eventName}!

Please find your personal QR code attached to this email. You'll need to present this QR code at the event entrance for quick check-in.

Important notes:
- This QR code is unique to you and should not be shared
- Please save this email or take a screenshot of the QR code
- The QR code will be valid for 24 hours from generation
- If you have any issues, please contact the event organizers

We look forward to seeing you at the event!

Best regards,
VTIX Scanner Team
    `.trim();

    return {
      qrCodeImage,
      qrCodeId,
      emailSubject,
      emailBody,
    };
  }
}
