import { create } from "zustand";
import { persist } from "zustand/middleware";
import type {
  Event,
  Attendee,
  AttendeeFormData,
  EventListItem,
} from "../types";
import { QRCodeService } from "../services/qrCodeService";
import { EventService } from "../services/eventService";

interface EventState {
  events: EventListItem[];
  attendees: Attendee[];
  selectedEvent: Event | null;
  isLoading: boolean;

  // Event actions
  getEvents: () => Promise<EventListItem[] | undefined>;
  getEvent: (id: string) => EventListItem | undefined;
  setSelectedEvent: (event: Event | null) => void;

  // Attendee actions
  addAttendee: (
    eventId: string,
    attendeeData: AttendeeFormData
  ) => Promise<Attendee>;
  removeAttendee: (attendeeId: string) => Promise<void>;
  getEventAttendees: (eventId: string) => Attendee[];
  checkInAttendee: (attendeeId: string) => Promise<void>;

  // Utility actions
  setLoading: (loading: boolean) => void;
  initializeMockData: () => void;
}

// This function is no longer needed as we're using real API data

// Mock attendees for demonstration - replace with real API data when available
const generateMockAttendees = (): Attendee[] => [];

// Helper function to ensure dates are Date objects
const ensureDateObjects = (data: any): any => {
  if (Array.isArray(data)) {
    return data.map(ensureDateObjects);
  } else if (data && typeof data === "object") {
    const result: any = {};
    for (const [key, value] of Object.entries(data)) {
      if (key.includes("Date") || key.includes("At")) {
        result[key] = value ? new Date(value as string) : value;
      } else {
        result[key] = ensureDateObjects(value);
      }
    }
    return result;
  }
  return data;
};

export const useEventStore = create<EventState>()(
  persist(
    (set, get) => ({
      events: [],
      attendees: [],
      selectedEvent: null,
      isLoading: false,

      deleteEvent: async (id: string) => {
        set({ isLoading: true });
        try {
          await EventService.deleteEvent(id);
          set((state) => ({
            events: state.events.filter((event) => event.id !== id),
            attendees: state.attendees.filter(
              (attendee) => attendee.eventId !== id
            ),
            selectedEvent:
              state.selectedEvent?.id === id ? null : state.selectedEvent,
            isLoading: false,
          }));
        } catch (error) {
          set({ isLoading: false });
          throw error;
        }
      },

      getEvents: async () => {
        set({ isLoading: true });
        try {
          const response = await EventService.getEvents();
          set({
            events: response,
            isLoading: false,
          });
          return response;
        } catch (error) {
          console.error('Failed to fetch events:', error);
          set({ isLoading: false });
          throw error;
        }
      },

      getEvent: (id: string) => {
        return get().events.find((event) => event.id === id);
      },

      setSelectedEvent: (event: Event | null) => {
        set({ selectedEvent: event });
      },

      addAttendee: async (eventId: string, attendeeData: AttendeeFormData) => {
        set({ isLoading: true });

        await new Promise((resolve) => setTimeout(resolve, 300));

        const attendeeId = Date.now().toString();
        const qrCodeId = QRCodeService.generateQRCodeId(attendeeId, eventId);

        const newAttendee: Attendee = {
          id: attendeeId,
          eventId,
          ...attendeeData,
          qrCode: qrCodeId,
          registeredAt: new Date(),
          checkedIn: false,
        };

        set((state) => ({
          attendees: [...state.attendees, newAttendee],
          isLoading: false,
        }));

        return newAttendee;
      },

      removeAttendee: async (attendeeId: string) => {
        set({ isLoading: true });

        await new Promise((resolve) => setTimeout(resolve, 200));

        set((state) => ({
          attendees: state.attendees.filter(
            (attendee) => attendee.id !== attendeeId
          ),
          isLoading: false,
        }));
      },

      getEventAttendees: (eventId: string) => {
        return get().attendees.filter(
          (attendee) => attendee.eventId === eventId
        );
      },

      checkInAttendee: async (attendeeId: string) => {
        set({ isLoading: true });

        await new Promise((resolve) => setTimeout(resolve, 200));

        set((state) => ({
          attendees: state.attendees.map((attendee) =>
            attendee.id === attendeeId
              ? {
                ...attendee,
                checkedIn: true,
                checkedInAt: new Date(),
              }
              : attendee
          ),
          isLoading: false,
        }));
      },

      setLoading: (loading: boolean) => {
        set({ isLoading: loading });
      },

      initializeMockData: () => {
        set({ 
          events: [], 
          attendees: generateMockAttendees() 
        });
      },
    }),
    {
      name: "event-storage",
      onRehydrateStorage: () => (state) => {
        if (state) {
          // Events from API don't need date conversion as they use Unix timestamps
          state.attendees = ensureDateObjects(state.attendees);
          state.selectedEvent = state.selectedEvent
            ? ensureDateObjects(state.selectedEvent)
            : null;
        }
      },
    }
  )
);
